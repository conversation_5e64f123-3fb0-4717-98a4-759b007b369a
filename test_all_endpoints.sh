#!/bin/bash

# PropBolt Comprehensive Endpoint Testing Script
# Tests all available endpoints systematically

set -e

echo "🚀 PropBolt Comprehensive Endpoint Testing Suite"
echo "=================================================="
echo "📍 Testing all available endpoints"
echo "🎯 Location: Daytona Beach, FL (Land Search Focus)"
echo ""

# Configuration
BASE_URL="http://localhost:8080"
PROD_URL="https://propbolt.com"
TEST_API_KEY="test-api-key-123"
REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914}"

# Test results directory
RESULTS_DIR="./test_results"
mkdir -p "$RESULTS_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test an endpoint
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local description="$3"
    local data="$4"
    local headers="$5"
    local expected_status="${6:-200}"
    
    echo -e "${BLUE}🔬 Testing:${NC} $method $endpoint"
    echo -e "${YELLOW}📝 Description:${NC} $description"
    
    # Prepare curl command
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    # Add headers if provided
    if [ -n "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    else
        curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    fi
    
    # Add data if provided
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    # Add endpoint
    curl_cmd="$curl_cmd $BASE_URL$endpoint"
    
    # Execute test
    local start_time=$(date +%s.%N)
    local response=$(eval $curl_cmd 2>/dev/null)
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")
    
    # Extract status code (last 3 characters)
    local status_code="${response: -3}"
    local response_body="${response%???}"
    
    # Save detailed results
    local test_name=$(echo "$endpoint" | sed 's/[^a-zA-Z0-9]/_/g')
    echo "Method: $method" > "$RESULTS_DIR/${test_name}_result.txt"
    echo "Endpoint: $endpoint" >> "$RESULTS_DIR/${test_name}_result.txt"
    echo "Status Code: $status_code" >> "$RESULTS_DIR/${test_name}_result.txt"
    echo "Duration: ${duration}s" >> "$RESULTS_DIR/${test_name}_result.txt"
    echo "Response Body:" >> "$RESULTS_DIR/${test_name}_result.txt"
    echo "$response_body" >> "$RESULTS_DIR/${test_name}_result.txt"
    
    # Check result
    if [ "$status_code" = "$expected_status" ] || [ "$status_code" = "200" ] || [ "$status_code" = "201" ]; then
        echo -e "${GREEN}✅ PASS${NC} - Status: $status_code (${duration}s)"
        if [ ${#response_body} -gt 100 ]; then
            echo -e "${BLUE}📊 Response:${NC} ${response_body:0:100}..."
        else
            echo -e "${BLUE}📊 Response:${NC} $response_body"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} - Status: $status_code (Expected: $expected_status)"
        echo -e "${RED}📊 Response:${NC} $response_body"
    fi
    echo ""
}

# Function to test with API key
test_with_api_key() {
    local method="$1"
    local endpoint="$2"
    local description="$3"
    local data="$4"
    local expected_status="${5:-200}"
    
    test_endpoint "$method" "$endpoint" "$description" "$data" "-H 'X-API-KEY: $TEST_API_KEY' -H 'Content-Type: application/json'" "$expected_status"
}

echo "🏁 Starting endpoint tests..."
echo ""

# ============================================================================
# PHASE 1: Basic Health and Status Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 1: Health and Status Endpoints${NC}"
echo "============================================"

test_endpoint "GET" "/" "Root health check endpoint"
test_endpoint "GET" "/status" "Proxy status endpoint"
test_endpoint "GET" "/test-proxy" "Proxy connectivity test"

# ============================================================================
# PHASE 2: Land Search Endpoints (Primary Focus)
# ============================================================================
echo -e "${BLUE}📋 PHASE 2: Land Search Endpoints${NC}"
echo "=================================="

test_endpoint "GET" "/api/test-land-search" "Basic land search test"
test_endpoint "GET" "/api/test-land-search?maxPrice=100000" "Land search with price filter"
test_endpoint "GET" "/api/test-land-search?maxPrice=50000" "Land search under $50k"

# Search endpoints with Daytona Beach coordinates
DAYTONA_PARAMS="neLat=29.3&neLong=-80.9&swLat=29.1&swLong=-81.1&pagination=1&zoom=12"
test_endpoint "GET" "/search?$DAYTONA_PARAMS" "Zillow search for Daytona Beach area"
test_endpoint "GET" "/search?$DAYTONA_PARAMS&priceMax=100000" "Zillow search with price limit"

# Dashboard search endpoint
SEARCH_DATA='{"query":"Daytona Beach, FL","filters":{"propertyType":"vacant_land","maxPrice":100000}}'
test_endpoint "POST" "/api/search" "Dashboard search for vacant land" "$SEARCH_DATA"

# ============================================================================
# PHASE 3: Property Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 3: Property Endpoints${NC}"
echo "==============================="

# Property details endpoints
test_endpoint "GET" "/property?address=123%20Main%20St%20Daytona%20Beach%20FL" "Property details by address"
test_endpoint "GET" "/propertyMinimal?address=123%20Main%20St%20Daytona%20Beach%20FL" "Minimal property details"
test_endpoint "GET" "/propertyImages?address=123%20Main%20St%20Daytona%20Beach%20FL" "Property images"

# Rent estimate
test_endpoint "GET" "/rentEstimate?address=123%20Main%20St%20Daytona%20Beach%20FL" "Rent estimate for property"

# ============================================================================
# PHASE 4: Search Endpoints (Different Types)
# ============================================================================
echo -e "${BLUE}📋 PHASE 4: Search Type Endpoints${NC}"
echo "================================="

test_endpoint "GET" "/search/sold?$DAYTONA_PARAMS" "Sold properties search"
test_endpoint "GET" "/search/rentals?$DAYTONA_PARAMS" "Rental properties search"

# ============================================================================
# PHASE 5: Dashboard and Admin Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 5: Dashboard Endpoints${NC}"
echo "==============================="

test_endpoint "GET" "/api/dashboard/stats" "Dashboard statistics"
test_endpoint "POST" "/api/sync-properties" "Property sync endpoint"
test_endpoint "POST" "/api/refresh-data" "Data refresh endpoint"

# Property analysis
ANALYSIS_DATA='{"address":"123 Main St, Daytona Beach, FL 32114"}'
test_endpoint "POST" "/api/property-analysis" "Property analysis endpoint" "$ANALYSIS_DATA"

# ============================================================================
# PHASE 6: RealEstateAPI Proxy Endpoints (Admin Only)
# ============================================================================
echo -e "${BLUE}📋 PHASE 6: RealEstateAPI Proxy Endpoints${NC}"
echo "=========================================="

# AutoComplete endpoint
AUTOCOMPLETE_DATA='{"search":"Daytona Beach, FL","search_types":["city","address"]}'
test_endpoint "POST" "/api/v1/proxy/autocomplete" "RealEstate API autocomplete" "$AUTOCOMPLETE_DATA"

# Property search via proxy
PROPERTY_SEARCH_DATA='{"city":"Daytona Beach","state":"FL","property_type":"LAND","value_max":100000,"size":10}'
test_endpoint "POST" "/api/v1/proxy/property-search" "RealEstate API property search" "$PROPERTY_SEARCH_DATA"

# Property detail via proxy
PROPERTY_DETAIL_DATA='{"address":"123 Main St, Daytona Beach, FL 32114","include_comps":true}'
test_endpoint "POST" "/api/v1/proxy/property-detail" "RealEstate API property detail" "$PROPERTY_DETAIL_DATA"

# Property mapping via proxy
MAPPING_DATA='{"city":"Daytona Beach","state":"FL","property_type":"LAND"}'
test_endpoint "POST" "/api/v1/proxy/mapping" "RealEstate API property mapping" "$MAPPING_DATA"

# Property comps via proxy
COMPS_DATA='{"address":"123 Main St, Daytona Beach, FL 32114","comp_count":5}'
test_endpoint "POST" "/api/v1/proxy/property-comps-v3" "RealEstate API property comps v3" "$COMPS_DATA"
test_endpoint "POST" "/api/v1/proxy/property-comps-v2" "RealEstate API property comps v2" "$COMPS_DATA"

# Liens and skiptrace
LIENS_DATA='{"address":"123 Main St, Daytona Beach, FL 32114"}'
test_endpoint "POST" "/api/v1/proxy/involuntary-liens" "RealEstate API involuntary liens" "$LIENS_DATA"

SKIPTRACE_DATA='{"first_name":"John","last_name":"Doe","address":"123 Main St, Daytona Beach, FL 32114"}'
test_endpoint "POST" "/api/v1/proxy/skiptrace" "RealEstate API skiptrace" "$SKIPTRACE_DATA"

# ============================================================================
# PHASE 7: API Key Protected Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 7: API Key Protected Endpoints${NC}"
echo "======================================"

# Data API endpoints (require API key)
test_with_api_key "GET" "/api/v1/data/health" "Data API health check"
test_with_api_key "GET" "/api/v1/data/search?city=Daytona%20Beach&state=FL&property_type=LAND" "Data API search"
test_with_api_key "GET" "/api/v1/data/search/sold?city=Daytona%20Beach&state=FL" "Data API sold search"
test_with_api_key "GET" "/api/v1/data/search/rentals?city=Daytona%20Beach&state=FL" "Data API rentals search"
test_with_api_key "GET" "/api/v1/data/property?id=123" "Data API property details"
test_with_api_key "GET" "/api/v1/data/autocomplete?search=Daytona%20Beach" "Data API autocomplete"

# ============================================================================
# PHASE 8: Authentication Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 8: Authentication Endpoints${NC}"
echo "==================================="

test_endpoint "GET" "/api/auth/user" "Get user info endpoint"
test_endpoint "GET" "/api/auth/get-user" "Get user info (alias)"

# User creation (signup)
USER_DATA='{"email":"<EMAIL>","password":"testpass123","name":"Test User"}'
test_endpoint "POST" "/api/auth/create-user" "Create user endpoint" "$USER_DATA"
test_endpoint "POST" "/api/auth/signup" "Signup endpoint (alias)" "$USER_DATA"

# ============================================================================
# PHASE 9: Static File Endpoints
# ============================================================================
echo -e "${BLUE}📋 PHASE 9: Static File Endpoints${NC}"
echo "================================="

test_endpoint "GET" "/favicon.ico" "Favicon endpoint"
test_endpoint "GET" "/api-logo.png" "API logo endpoint"
test_endpoint "GET" "/byte-media-logo-v2.png" "Byte Media logo endpoint"

# Dashboard pages
test_endpoint "GET" "/dashboard" "Dashboard page"
test_endpoint "GET" "/admin" "Admin page"

# ============================================================================
# PHASE 10: Production URL Testing
# ============================================================================
echo -e "${BLUE}📋 PHASE 10: Production URL Testing${NC}"
echo "==================================="

echo "Testing production endpoints at $PROD_URL..."

# Test key production endpoints
curl -s -w '%{http_code}' -X GET "$PROD_URL/" > "$RESULTS_DIR/prod_health.txt" 2>&1 || echo "Production health check failed"
curl -s -w '%{http_code}' -X GET "$PROD_URL/api/test-land-search?maxPrice=100000" > "$RESULTS_DIR/prod_land_search.txt" 2>&1 || echo "Production land search failed"

echo ""
echo "🎉 COMPREHENSIVE ENDPOINT TESTING COMPLETED!"
echo "============================================="
echo ""
echo "📊 Test Summary:"
echo "📁 Results Directory: $RESULTS_DIR"
echo "📄 Individual test results saved as: [endpoint_name]_result.txt"
echo ""
echo "🔍 To view specific results:"
echo "cat $RESULTS_DIR/[test_name]_result.txt"
echo ""
echo "📈 Quick Results Overview:"
ls -la "$RESULTS_DIR"/*.txt 2>/dev/null | wc -l | xargs echo "Total test files created:"
echo ""
echo "✅ Land Search Focus Results:"
echo "- Basic land search test: $(grep -l 'test-land-search' $RESULTS_DIR/*.txt | wc -l) tests"
echo "- Zillow search tests: $(grep -l 'search.*Daytona' $RESULTS_DIR/*.txt | wc -l) tests"
echo "- RealEstate API tests: $(grep -l 'proxy.*property' $RESULTS_DIR/*.txt | wc -l) tests"
echo ""
echo "🎯 Next Steps:"
echo "1. Review failed tests in the results directory"
echo "2. Fix any broken endpoints"
echo "3. Compare land search results with Zillow.com"
echo "4. Validate data accuracy for Daytona Beach, FL"
echo ""
